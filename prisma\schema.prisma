// pc-shutdown-task-func/prisma/schema.prisma
generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model TASKS {
  TASK_ID           String     @id @default(uuid()) @db.Uuid
  TASK_NAME         String     @db.Var<PERSON>har(256)
  REQUESTING_USER_ID String     @db.VarChar(256)
  TASK_TYPE         String?    @db.Var<PERSON><PERSON>(50)
  PARAMETERS        String?    @db.Text
  STATUS            String?    @db.VarChar(30)
  CREATED_AT        DateTime   @default(now())
  STARTED_AT        DateTime? 
  COMPLETED_AT      DateTime?
  ZIP_BLOB_PATH     String?    @db.Text
  ERROR_MESSAGE     String?    @db.Text
}

model USER_ENV_LOG {
  ID           String     @id @default(uuid()) @db.Uuid
  USER_ID      String     @db.VarChar(64)      @unique
  LOG_TIME     DateTime? 
  UPDATE_TIME  DateTime
  UPDATE_USER  String     @db.Var<PERSON>har(256)
  SCHEMA_NAME  String     @db.Var<PERSON>har(256)
}

enum Status {
  PENDING
  COMPRESSING
  COMPLETED
  FAILED
}