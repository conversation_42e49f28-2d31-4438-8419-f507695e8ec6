import {
  app,
  HttpRequest,
  HttpResponseInit,
  InvocationContext,
} from "@azure/functions";
import { BlobServiceClient } from "@azure/storage-blob";
import { PrismaClient, TASKS } from "@prisma/client"; // Prisma関連の型をインポート
import archiver from "archiver";
import { PassThrough, Readable } from "stream";
import { finished } from "stream/promises";

// Prisma Clientのインスタンスを初期化
const prisma = new PrismaClient();

// タスクの状態を管理するためのEnum定義 (schema.prismaと一致)
// コード内でマジックストリング（例："PENDING"）を直接使用するのを防ぐ
enum Status {
  PENDING = "PENDING",
  COMPRESSING = "COMPRESSING",
  COMPLETED = "COMPLETED",
  FAILED = "FAILED",
}

/**
 * Azure Function: CompressTaskProcessor
 * HTTPトリガーで起動し、指定されたtaskIdに基づいてログファイルを検索・圧縮し、
 * 結果をBlob Storageにアップロード後、タスクの状態を更新する。
 * @param request - HTTPリクエストオブジェクト
 * @param context - 呼び出しコンテキストオブジェクト
 * @returns HTTPレスポンス
 */
export async function CompressTaskProcessor(
  request: HttpRequest,
  context: InvocationContext
): Promise<HttpResponseInit> {
  const functionName = "CompressTaskProcessor";
  context.info(
    `[${functionName}] HTTPトリガー関数がリクエストを処理しました。`
  );

  // 関数の起動とパラメータ検証
  let taskId: string;
  try {
    const body = (await request.json()) as { taskId?: string };
    // リクエストボディにtaskIdが存在し、かつ文字列型であることを検証
    if (!body || !body.taskId || typeof body.taskId !== "string") {
      context.error("[検証エラー] 'taskId' が見つからないか、形式が無効です。");
      return { status: 400, body: "Bad Request: 'taskId'が必要です。" };
    }
    taskId = body.taskId;
    context.info(`[ステップ1] パラメータ検証完了。taskId: ${taskId}`);
  } catch (error) {
    context.error(
      "[検証エラー] リクエストボディのJSON解析に失敗しました。",
      error
    );
    return { status: 400, body: "Bad Request: JSON形式が無効です。" };
  }

  // タスクのロック
  let task: TASKS;
  try {
    // taskIdを使用して、TASKSテーブルから一意のタスクを検索
    const taskRecord = await prisma.tASKS.findUnique({
      where: { TASK_ID: taskId },
    });

    // タスクが存在しない場合は、競合エラーとして処理
    if (!taskRecord) {
      context.warn(
        `[ステップ2] タスクロック失敗。タスク '${taskId}' が見つかりません。`
      );
      return { status: 409, body: `Conflict: タスクが見つかりません。` };
    }
    // タスクの状態がPENDINGでない場合は、既に処理中または処理済みと判断
    if (taskRecord.STATUS !== Status.PENDING) {
      context.warn(
        `[ステップ2] タスクロック失敗。タスク '${taskId}' の状態がPENDINGではありません。現在の状態: ${taskRecord.STATUS}`
      );
      return {
        status: 409,
        body: `Conflict: タスクは既に処理済みか、処理中です。`,
      };
    }

    // タスクの状態をCOMPRESSINGに更新し、処理開始時間を記録（アトミック操作）
    task = await prisma.tASKS.update({
      where: { TASK_ID: taskId },
      data: { STATUS: Status.COMPRESSING, STARTED_AT: new Date() },
    });
    context.info(
      `[ステップ2] タスク '${taskId}' をロックしました。状態 -> COMPRESSING`
    );
  } catch (dbError) {
    context.error(
      `[ステップ2] タスクロック中のDBエラー。taskId: '${taskId}'`,
      dbError
    );
    return {
      status: 500,
      body: "Internal Server Error: タスクのロックに失敗しました。",
    };
  }

  // 環境変数の読み込みと検証
  const {
    AZURE_STORAGE_CONNECTION_STRING: connectionString,
    AZURE_STORAGE_LOG_CONTAINER_NAME: sourceContainer,
    AZURE_STORAGE_TEMP_ZIP_CONTAINER_NAME: destinationContainer,
    AZURE_STORAGE_TEMP_ZIP_BASE_PATH: destinationBasePath,
    AZURE_STORAGE_LOG_BASE_PATH: logBasePath, // ソースログのベースパス
  } = process.env;

  // 必須の環境変数が設定されているかを確認
  if (
    !connectionString ||
    !sourceContainer ||
    !destinationContainer ||
    destinationBasePath === undefined ||
    !logBasePath
  ) {
    const errorMsg =
      "サーバー設定エラー：必須のストレージ関連環境変数が設定されていません。";
    context.error(`[設定エラー] ${errorMsg}`);
    // タスク状態をFAILEDに更新してから終了
    await updateTaskToFailed(taskId, errorMsg, context);
    return { status: 500, body: errorMsg };
  }

  // 圧縮ファイルに少なくとも1つのファイルが追加されたかを追跡するフラグ
  let hasAddedAnyFiles = false;
  // 生成されるZIPファイルのBlob Storage上のフルパスを構築
  const zipBlobPath = `${destinationBasePath ? destinationBasePath + "/" : ""}${
    task.TASK_ID
  }.zip`;

  // コアとなる圧縮・アップロード処理
  try {
    // タスクパラメータ（USER_IDの配列）を解析
    if (!task.PARAMETERS) {
      throw new Error("タスクパラメータがnullまたは空です。");
    }
    const userIds = JSON.parse(task.PARAMETERS) as string[];
    if (!Array.isArray(userIds)) {
      throw new Error(
        "タスクパラメータが有効なUSER_IDのJSON配列ではありません。"
      );
    }

    // archiverインスタンスとBlob Serviceクライアントを初期化
    const archive = archiver("zip", { zlib: { level: 6 } }); // 圧縮レベル6（速度と圧縮率のバランス）
    archive.on("warning", (err) =>
      context.warn(`[Archiver警告] taskId ${taskId}:`, err)
    );

    const blobServiceClient =
      BlobServiceClient.fromConnectionString(connectionString);
    const sourceContainerClient =
      blobServiceClient.getContainerClient(sourceContainer);
    const destinationBlobClient = blobServiceClient
      .getContainerClient(destinationContainer)
      .getBlockBlobClient(zipBlobPath);

    // ストリーム処理の設定（メモリ効率化のため）
    const passThroughStream = new PassThrough(); // 中継用のストリーム
    archive.pipe(passThroughStream); // 圧縮データの出力を中継ストリームに接続
    // 中継ストリームの出力をBlob Storageへのアップロードストリームに接続
    const uploadPromise = destinationBlobClient.uploadStream(passThroughStream);

    context.info(
      `'${taskId}'のタスクで、${userIds.length}件のユーザーIDを処理します。`
    );

    // 各ユーザーIDをループ処理
    for (const userId of userIds) {
      try {
        // ユーザーのSCHEMA_NAMEを取得
        context.info(`[ユーザー処理] ユーザーのログ情報を取得中: ${userId}`);
        const userLogInfo = await prisma.uSER_ENV_LOG.findUnique({
          where: { USER_ID: userId },
          select: { SCHEMA_NAME: true },
        });

        if (!userLogInfo || !userLogInfo.SCHEMA_NAME) {
          context.warn(
            `[ユーザー処理] ユーザー'${userId}'のログ情報またはSCHEMA_NAMEが見つかりません。スキップします。`
          );
          continue; // 次のユーザーへ
        }

        // Blobのディレクトリパス（プレフィックス）を構築
        const folderPath = [userLogInfo.SCHEMA_NAME, logBasePath, userId].join(
          "/"
        );
        const directoryPrefix = `${folderPath}/`;
        context.info(
          `[ユーザー処理] Blobをリストアップするプレフィックス: '${directoryPrefix}'`
        );

        // 指定されたプレフィックスを持つ全てのBlobをリストアップ
        const blobs = sourceContainerClient.listBlobsFlat({
          prefix: directoryPrefix,
        });

        // 検出された各Blobを圧縮ファイルに追加
        for await (const blob of blobs) {
          const filePath = blob.name;
          context.info(`[ファイル処理] アーカイブに追加中: ${filePath}`);
          try {
            const sourceBlobClient =
              sourceContainerClient.getBlobClient(filePath);
            const downloadResponse = await sourceBlobClient.download();

            if (!downloadResponse.readableStreamBody) {
              context.warn(
                `[ファイル処理] '${filePath}'のストリームを取得できませんでした。スキップします。`
              );
              continue;
            }
            // ファイルストリームをアーカイブに追加
            archive.append(downloadResponse.readableStreamBody as Readable, {
              name: filePath, // ZIPファイル内でのパスとファイル名
            });
            hasAddedAnyFiles = true; // ファイルが追加されたことを記録
          } catch (fileError) {
            context.error(
              `[ファイル処理] '${filePath}'の処理中にエラーが発生しました。スキップします。`,
              fileError
            );
          }
        }
      } catch (userProcessingError) {
        context.error(
          `[ユーザー処理] ユーザー'${userId}'の処理中にエラーが発生しました。このユーザーをスキップします。`,
          userProcessingError
        );
        continue; // 次のユーザーの処理を継続
      }
    }

    // アーカイブのファイナライズとアップロードの完了を待機
    context.info(
      `[ファイナライズ] アーカイブを完成させています。taskId: '${taskId}'`
    );
    archive.finalize(); // 全てのファイルの追加が完了したことを通知

    await finished(passThroughStream); // 中継ストリームが完全に終了するのを待つ
    await uploadPromise; // Blobのアップロードが完了するのを待つ
    context.info(
      `[ファイナライズ] Blobのアップロードが完了しました。taskId: '${taskId}'`
    );

    // 最終結果の更新
    if (hasAddedAnyFiles) {
      // ケース①: 成功。少なくとも1つのファイルが圧縮された場合
      await prisma.tASKS.update({
        where: { TASK_ID: taskId },
        data: {
          STATUS: Status.COMPLETED,
          COMPLETED_AT: new Date(),
          ZIP_BLOB_PATH: zipBlobPath,
          ERROR_MESSAGE: null, // エラーメッセージをクリア
        },
      });
      context.info(
        `[ステップ4] タスク '${taskId}' の処理が完了しました。状態 -> COMPLETED`
      );
    } else {
      // ケース②: ビジネスロジック的失敗。圧縮すべきファイルが一つもなかった場合
      await prisma.tASKS.update({
        where: { TASK_ID: taskId },
        data: {
          STATUS: Status.FAILED,
          COMPLETED_AT: new Date(),
          ERROR_MESSAGE: "ログ情報が存在しません。",
        },
      });
      context.warn(
        `[ステップ4] タスク '${taskId}' に追加するファイルがありませんでした。状態 -> FAILED`
      );
    }
  } catch (error) {
    // ケース③: 技術的失敗。予期せぬ例外が発生した場合
    const errorMessage = error instanceof Error ? error.message : String(error);
    context.error(
      `[ステップ3 Catch] 重大なエラーが発生しました。taskId: '${taskId}'`,
      error
    );
    await updateTaskToFailed(taskId, errorMessage, context);
  }

  // 関数の終了
  context.info(
    `[${functionName}] taskId: '${taskId}' の全処理が終了しました。`
  );
  return { status: 200, body: `タスク ${taskId} の処理が完了しました。` };
}

/**
 * 補助関数：指定されたタスクの状態をFAILEDに更新する。
 * @param taskId - 更新対象のタスクID
 * @param errorMessage - 記録するエラーメッセージ
 * @param context - ログ出力用の呼び出しコンテキスト
 */
async function updateTaskToFailed(
  taskId: string,
  errorMessage: string,
  context: InvocationContext
): Promise<void> {
  try {
    await prisma.tASKS.update({
      where: { TASK_ID: taskId },
      data: {
        STATUS: Status.FAILED,
        COMPLETED_AT: new Date(),
        ERROR_MESSAGE: errorMessage.substring(0, 1000), // DBのフィールド長を超えないように切り詰める
      },
    });
    context.info(
      `[UpdateFailed] タスク '${taskId}' の状態をFAILEDに正常に更新しました。`
    );
  } catch (dbError) {
    // この補助関数自体が失敗した場合（例：DB接続断）、これは非常に深刻な状況
    context.error(
      `[UpdateFailed] 致命的エラー：タスク '${taskId}' の状態をFAILEDに更新できませんでした。StaleTaskCleanupFuncによる回復が必要です。`,
      dbError
    );
  }
}

// HTTPトリガーとして関数を登録
app.http("CompressTaskProcessor", {
  methods: ["POST"],
  authLevel: "function", // APIキーによる認証が必要
  handler: CompressTaskProcessor,
});
