# **圧縮タスク処理 Azure Functions プロジェクト**

## 1. プロジェクト概要 (Project Overview)

本プロジェクトは、ユーザー環境ログの非同期圧縮処理を担当する Azure Functions アプリケーションです。フロントエンドからのリクエストに基づき、バックグラウンドで複数のユーザーログを一つの ZIP ファイルにまとめ、ダウンロード可能な状態にする責務を担います。

このプロジェクトには、主に以下の二つの独立した関数が含まれており、それぞれが連携してタスクのライフサイクル全体を管理します。

- **`CompressTaskProcessor`**: 圧縮タスクの実行を担当するメインの処理関数。
- **`TimeoutTaskCleanup`**: 予期せぬエラーでスタックしたタスクを自動的にクリーンアップするメンテナンス関数。

## 2. 技術スタック (Technology Stack)

- **実行環境**: Azure Functions
- **言語**: TypeScript, Node.js
- **主要ライブラリ**:
  - `@azure/functions`: Azure Functions v4 プログラミングモデル
  - `@azure/storage-blob`: Azure Blob Storage との連携
  - `pg` または `tedious`: データベース接続
  - `archiver`: ファイルのストリーミング圧縮
- **依存サービス**:
  - Azure Blob Storage: ログファイル（ソース）と生成された ZIP ファイル（ターゲット）の保存
  - Azure SQL Database / PostgreSQL: タスクの状態管理

## 3. 環境構築 (Getting Started)

開発環境で本プロジェクトを実行するための手順は以下の通りです。

#### 3.1. 前提条件 (Prerequisites)

- [Node.js](https://nodejs.org/) (LTS バージョンを推奨)
- [Azure Functions Core Tools](https://docs.microsoft.com/ja-jp/azure/azure-functions/functions-run-local) (v4)
- [Git](https://git-scm.com/)

#### 3.2. インストールと設定 (Installation & Setup)

1.  **リポジトリのクローン:**

    ```bash
    git clone [your-gitlab-repository-url]
    cd [project-folder]
    ```

2.  **依存関係のインストール:**

    ```bash
    npm install
    ```

3.  **ローカル設定ファイルの作成:**
    プロジェクトのルートディレクトリに`local.settings.json`という名前のファイルを作成します。このファイルは Git の管理対象外（`.gitignore`に含まれる）であり、ローカル環境の機密情報（接続文字列など）を格納します。

    以下のテンプレートをコピーして`local.settings.json`に貼り付け、各自のローカル開発環境に合わせて値を設定してください。

    ```json
    {
      "IsEncrypted": false,
      "Values": {
        "AzureWebJobsStorage": "UseDevelopmentStorage=true",
        "FUNCTIONS_WORKER_RUNTIME": "node",
        "AzureWebJobsFeatureFlags": "EnableWorkerIndexing",

        "DATABASE_URL": "[あなたのローカルDB接続URL]",
        "AZURE_STORAGE_CONNECTION_STRING": "[あなたのAzure Storage接続文字列]",
        "AZURE_STORAGE_LOG_CONTAINER_NAME": "customer",
        "AZURE_STORAGE_LOG_BASE_PATH": "userenvlogs",
        "AZURE_STORAGE_TEMP_ZIP_CONTAINER_NAME": "media",
        "AZURE_STORAGE_TEMP_ZIP_BASE_PATH": "temp_zips",
        "TIMEOUT_THRESHOLD": "7200"
      }
    }
    ```

    - **注意**: `DATABASE_URL`と`AZURE_STORAGE_CONNECTION_STRING`は、必ず各自のローカル開発環境に合わせて設定してください。その他の値は、通常は変更不要です。

#### 3.3. 実行 (Running the Project)

以下のコマンドでローカル開発サーバーを起動します。

```bash
npm run build && npm start
```

- _備考：`npm run build`は、TypeScript コードを JavaScript にコンパイルします。`npm start`は、コンパイルされたコードを使用して Azure Functions のローカルホストを起動します。_

サーバーが正常に起動すると、各関数のエンドポイントがコンソールに表示されます。

## 4. 環境変数 (Environment Variables)

本プロジェクトで利用する主要な環境変数は以下の通りです。`local.settings.json`（ローカル開発用）または Azure ポータルの「構成」（本番/ステージング環境用）で設定する必要があります。

| 変数名                                  | 説明                                                                                                             | 設定例                                |
| :-------------------------------------- | :--------------------------------------------------------------------------------------------------------------- | :------------------------------------ |
| `DATABASE_URL`                          | タスク管理テーブル（`TASKS`）が存在するデータベースへの完全な接続 URL。                                          | `postgresql://user:pass@host:port/db` |
| `AZURE_STORAGE_CONNECTION_STRING`       | ソースログと生成 ZIP ファイルを格納する Azure Storage アカウントへの接続文字列。                                 | `DefaultEndpointsProtocol=...`        |
| `AZURE_STORAGE_LOG_CONTAINER_NAME`      | **ソースファイルコンテナ名。**<br>利用者環境ログ（ソースファイル）が格納されている Blob コンテナの名前。         | `customer`                            |
| `AZURE_STORAGE_LOG_BASE_PATH`           | **ソースファイルのベースパス。**<br>上記コンテナ内で、ユーザーごとのログが格納されるベースディレクトリ名。       | `userenvlogs`                         |
| `AZURE_STORAGE_TEMP_ZIP_CONTAINER_NAME` | **一時 ZIP ファイル用コンテナ名。**<br>生成された一時 ZIP ファイルをアップロードするための Blob コンテナの名前。 | `media`                               |
| `AZURE_STORAGE_TEMP_ZIP_BASE_PATH`      | **一時 ZIP ファイルのベースパス。**<br>上記コンテナ内で、ZIP ファイルが格納されるベースディレクトリ名。          | `temp_zips`                           |
| `TIMEOUT_THRESHOLD`                     | **タイムアウト閾値（秒）。**<br>`TimeoutTaskCleanup`関数がタスクを「タイムアウト」と判断するまでの秒数。         | `7200` (2 時間)                       |

#### **参考：ファイルパスの完全な構造**

- **利用者環境ログ（ソース）のパス構造:**
  - ソースファイルは、以下の構造を持つディレクトリ配下に格納されています。
  ```
  /${SCHEMA_NAME}/${AZURE_STORAGE_LOG_BASE_PATH}/${USER_ID}/
  ```
- **一時 ZIP ファイル（ターゲット）のパス構造:**
  - 生成される ZIP ファイルは、以下のパスで保存されます。
  ```
  /{AZURE_STORAGE_TEMP_ZIP_BASE_PATH}/${TASK_ID}.zip
  ```

## 5. 関数一覧 (Azure Functions Endpoints)

---

### **a. `CompressTaskProcessor`**

- **責務**: 指定された`taskId`に基づき、実際のログ圧縮とアップロード処理を実行します。
- **トリガー**: HTTP Trigger

| 項目                 | 詳細                                                                                                                                                                                       |
| :------------------- | :----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **エンドポイント**   | `/api/CompressTaskProcessor`                                                                                                                                                               |
| **メソッド**         | `POST`                                                                                                                                                                                     |
| **リクエストボディ** | `{ "taskId": "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx" }`                                                                                                                                     |
| **成功レスポンス**   | **`202 Accepted`**: リクエストが正常に受理され、非同期処理が開始されたことを示します。レスポンスボディは空です。                                                                           |
| **エラーレスポンス** | - **`400 Bad Request`**: `taskId`がリクエストに含まれていない、または形式が無効な場合。<br>- **`409 Conflict`**: 指定されたタスクが存在しない、または既に処理中（`PENDING`でない）の場合。 |

---

### **b. `TimeoutTaskCleanup`**

- **責務**: 定期的に実行され、長時間`COMPRESSING`状態でスタックしている「ゾンビタスク」を検出し、`FAILED`状態に更新します。
- **トリガー**: Timer Trigger

| 項目             | 詳細                                                                                                                                 |
| :--------------- | :----------------------------------------------------------------------------------------------------------------------------------- |
| **スケジュール** | **5 分に一度**<br>このスケジュールは、関数の登録コード（`app.timer()`）内で`'0 */5 * * * *'`という CRON 式によって設定されています。 |
| **処理内容**     | `STARTED_AT`が`TIMEOUT_THRESHOLD`で設定された時間を超えても`COMPRESSING`状態のタスクを検索し、一括で`FAILED`に更新します。           |

---

## 6. デプロイ (Deployment)

本プロジェクトの Azure へのデプロイは、以下の方法で行うことができます。

- **VS Code の Azure Functions 拡張機能**: 手動でのデプロイに最も簡単で推奨される方法です。
- **GitLab CI/CD**: `main`ブランチへのマージなどをトリガーに、自動でビルドとデプロイを行う CI/CD パイプラインを構築することを強く推奨します。

## 7. 開発・運用ルール (Development & Contribution)

- **ブランチ戦略**: [Git-flow](https://danielkummer.github.io/git-flow-cheatsheet/index.ja_JP.html) や [GitHub Flow](https://docs.github.com/ja/get-started/quickstart/github-flow) などの標準的なブランチ戦略に従ってください。新しい機能や修正は、`feature/`や`fix/`などのプレフィックスを付けたブランチで開発し、マージリクエスト（Merge Request）を作成してください。
- **コードスタイル**: プロジェクトに導入されているリンター（ESLint など）やフォーマッター（Prettier など）のルールに従ってください。コミット前に自動でフォーマットが実行されるように設定することが望ましいです。
- **ログの確認**: Azure ポータル上の Application Insights にて、関数の実行ログやエラーを確認できます。ローカルでのデバッグで再現しない問題が発生した場合、まず最初に確認すべき場所です。
