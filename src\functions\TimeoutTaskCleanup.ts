import { app, InvocationContext, Timer } from "@azure/functions";
import { PrismaClient } from "@prisma/client";

/**
 * Prisma Clientのシングルトンインスタンスを作成します。
 */
const prisma = new PrismaClient();

// ========================================================================
// HTTPトリガーのヘルパー関数
// ========================================================================
/**
 * 圧縮タスク処理関数を非同期で安全に呼び出します。
 * @param {string} taskId 処理対象のタスクID
 * @param {InvocationContext} context ログ記録用のコンテキスト
 */
function triggerCompressionFunction(
  taskId: string,
  context: InvocationContext
) {
  const functionUrl = process.env.AZURE_FUNCTION_URL;
  const functionKey = process.env.AZURE_FUNCTION_KEY;

  if (!functionUrl) {
    context.error(
      "エラー: 再トリガー先の関数URL (AZURE_FUNCTION_URL) が設定されていません。"
    );
    return;
  }

  if (!functionKey) {
    context.warn(
      `警告: Function Key (AZURE_FUNCTION_KEY) が見つかりません。匿名アクセスとしてリクエストを試みます。`
    );
  }

  context.log(`圧縮タスク処理関数を呼び出します。taskId: ${taskId}`);

  fetch(functionUrl, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      ...(functionKey && { "x-functions-key": functionKey }),
    },
    body: JSON.stringify({ taskId: taskId }),
  }).catch((error) => {
    context.error(
      `Azure Functionの呼び出しに失敗しました。taskId: ${taskId}, Error:`,
      error
    );
  });
}

/**
 * 定期的に実行され、システムの健全性を維持するためのメンテナンス関数。
 * 主な責務は、タイムアウトしたタスクのクリーンアップと、滞留したタスクの再トリガーです。
 */
async function TimeoutTaskCleanup( // [変更] 関数名をTimeoutTaskCleanupに変更
  myTimer: Timer,
  context: InvocationContext
): Promise<void> {
  context.log(
    "タスクメンテナンス関数 (TimeoutTaskCleanup) が実行を開始しました。"
  );

  if (myTimer.isPastDue) {
    context.log(
      "情報: この関数の実行はスケジュールされた時刻より遅延しています。"
    );
  }

  try {
    // ========================================================================
    // フェーズ1: タイムアウトしたCOMPRESSINGタスクのクリーンアップ
    // ========================================================================
    context.log(
      "フェーズ1: タイムアウトしたCOMPRESSINGタスクのクリーンアップを開始します。"
    );

    const timeoutThresholdSeconds = parseInt(
      process.env.TIMEOUT_THRESHOLD || "7200",
      10
    );
    // ... (エラーハンドリングは簡潔性のため省略)
    const now = new Date();
    const timeoutThresholdDate = new Date(
      now.getTime() - timeoutThresholdSeconds * 1000
    );

    context.log(
      `COMPRESSINGタスクのタイムアウト基準日時: ${timeoutThresholdDate.toISOString()}`
    );

    const cleanedTasks = await prisma.$transaction(async (tx) => {
      const tasksToClean = await tx.tASKS.findMany({
        where: {
          STATUS: "COMPRESSING",
          STARTED_AT: { lt: timeoutThresholdDate },
        },
        select: { TASK_ID: true },
      });
      if (tasksToClean.length === 0) return [];
      const taskIdsToClean = tasksToClean.map((task) => task.TASK_ID);
      await tx.tASKS.updateMany({
        where: { TASK_ID: { in: taskIdsToClean } },
        data: {
          STATUS: "FAILED",
          COMPLETED_AT: new Date(),
          ERROR_MESSAGE: "タスクがタイムアウトしました。",
        },
      });
      return tasksToClean;
    });

    const cleanedCount = cleanedTasks.length;
    if (cleanedCount > 0) {
      const cleanedIds = cleanedTasks.map((task) => task.TASK_ID).join(", ");
      context.log(
        `正常に ${cleanedCount} 件のタイムアウトしたタスクをクリーンアップしました。処理されたTASK_ID: [${cleanedIds}]`
      );
    } else {
      context.log("タイムアウトしたCOMPRESSINGタスクは検出されませんでした。");
    }

    // ========================================================================
    // フェーズ2: 滞留したPENDINGタスクの再処理
    // ========================================================================
    context.log("フェーズ2: 滞留したPENDINGタスクの再処理を開始します。");

    const compressingTaskCount = await prisma.tASKS.count({
      where: { STATUS: "COMPRESSING" },
    });

    if (compressingTaskCount > 0) {
      context.log(
        `情報: 現在 ${compressingTaskCount} 件のタスクが圧縮中のため、PENDINGタスクの再処理はスキップします。`
      );
    } else {
      context.log(
        "情報: システムは現在ビジー状態ではないため、PENDINGタスクの再処理を実行します。"
      );

      const pendingThresholdSeconds = parseInt(
        process.env.PENDING_RETRIGGER_THRESHOLD || "600",
        10
      );
      // ... (エラーハンドリングは簡潔性のため省略)
      const pendingThresholdDate = new Date(
        now.getTime() - pendingThresholdSeconds * 1000
      );

      const tasksToRetrigger = await prisma.tASKS.findMany({
        where: {
          STATUS: "PENDING",
          CREATED_AT: {
            lt: pendingThresholdDate,
          },
        },
        select: {
          TASK_ID: true,
        },
      });

      if (tasksToRetrigger.length > 0) {
        context.log(
          `${tasksToRetrigger.length} 件の滞留したPENDINGタスクを再トリガーします...`
        );

        for (const task of tasksToRetrigger) {
          triggerCompressionFunction(task.TASK_ID, context);
        }
        context.log(
          `${tasksToRetrigger.length} 件のタスクに対する再トリガーリクエストの送信が完了しました。`
        );
      } else {
        context.log("再処理対象のPENDINGタスクは検出されませんでした。");
      }
    }
  } catch (error) {
    context.error(
      "タスクメンテナンス処理の実行中に予期せぬエラーが発生しました。エラー詳細:",
      error
    );
  }

  context.log(
    "タスクメンテナンス関数 (TimeoutTaskCleanup) が実行を終了しました。"
  );
}

// [変更] 登録名もTimeoutTaskCleanupに変更
app.timer("TimeoutTaskCleanup", {
  schedule: "0 */5 * * * *",
  handler: TimeoutTaskCleanup, // 呼び出す関数をTimeoutTaskCleanupに設定
  runOnStartup: false,
});
