{"name": "pc-shutdown-task-func", "version": "1.0.0", "description": "", "main": "dist/index.js", "scripts": {"build": "prisma generate && tsc", "watch": "tsc -w", "clean": "<PERSON><PERSON><PERSON> dist", "prestart": "npm run clean && npm run build", "start": "func start", "test": "echo \"No tests yet...\""}, "dependencies": {"@azure/functions": "^4.7.2", "@azure/storage-blob": "^12.27.0", "@prisma/client": "^6.12.0", "archiver": "^7.0.1"}, "devDependencies": {"@types/archiver": "^6.0.3", "@types/node": "18.x", "azure-functions-core-tools": "^4.x", "prisma": "^6.12.0", "rimraf": "^5.0.0", "typescript": "^5.8.3"}}